import { Box, Typography, Button } from "@mui/material";
import StatsCard from "./StatsCard";
import { TrendingDown, TrendingUp } from "@mui/icons-material";
import TotalUsers from "./TotalUsers";
import TrafficWebsite from "./TrafficWebsite";

function MainContent() {
  return (
    <Box
      sx={{
        flex: 1,
        p: 3,
        bgcolor: "#fff",
        overflow: "auto",
      }}
    >
      <Box
        sx={{
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
          mb: 3,
        }}
      >
        <Typography variant="h4" component="h1" sx={{ fontWeight: "bold" }}>
          Overview
        </Typography>

        <Button variant="text" size="small" sx={{ color: "#000" }}>
          Today ▼
        </Button>
      </Box>

      {/* stats card */}
      <Box
        sx={{
          display: "flex",
          justifyContent: "space-between",
          gap: 3,
          mb: 4,
        }}
      >
        <StatsCard
          title="Views"
          value="7,265"
          percent="+11.01%"
          icon={<TrendingUp />}
          bgColor="#eceefb"
        />

        <StatsCard
          title="Visits"
          value="3,671"
          percent="-0.03%"
          icon={<TrendingDown />}
          bgColor="#e7f1fd"
        />

        <StatsCard
          title="New Users"
          value="156"
          percent="+15.03%"
          icon={<TrendingUp />}
          bgColor="#edeefb"
        />

        <StatsCard
          title="Active Users"
          value="2,318"
          percent="+6.08%"
          icon={<TrendingUp />}
          bgColor="#e7f1fd"
        />
      </Box>

      {/* total user + traffic website */}
      <Box sx={{ display: "flex", gap: 3 }}>
        {/* total user */}
        <Box sx={{ flex: 1 }}>
          <TotalUsers />
        </Box>
        {/* traffic website */}
        <Box>
          <TrafficWebsite />
        </Box>
      </Box>
    </Box>
  );
}

export default MainContent;
