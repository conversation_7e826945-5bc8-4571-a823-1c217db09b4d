import { Box, Typography, LinearProgress } from "@mui/material";

function TrafficWebsite() {
  const trafficData = [
    { name: "Google", percentage: 25 },
    { name: "YouTube", percentage: 45 },
    { name: "Instagram", percentage: 30 },
    { name: "Pinterest", percentage: 60 },
    { name: "Facebook", percentage: 20 },
    { name: "Twitter", percentage: 35 },
  ];

  return (
    <Box
      sx={{
        bgcolor: "#f8f9fa",
        p: 3,
        borderRadius: 2,
        width: 300,
        height: "fit-content",
      }}
    >
      <Typography
        variant="h6"
        sx={{
          fontWeight: "bold",
          mb: 3,
          color: "#333",
        }}
      >
        Traffic by Website
      </Typography>

      <Box sx={{ display: "flex", flexDirection: "column", gap: 3 }}>
        {trafficData.map((item, index) => (
          <Box key={index}>
            <Typography
              variant="body1"
              sx={{
                color: "#333",
                fontWeight: 400,
                mb: 1.5,
                fontSize: "16px",
              }}
            >
              {item.name}
            </Typography>
            <LinearProgress
              variant="determinate"
              value={item.percentage}
              sx={{
                height: 8,
                borderRadius: 4,
                bgcolor: "#e0e0e0",
                "& .MuiLinearProgress-bar": {
                  bgcolor: "#333",
                  borderRadius: 4,
                },
              }}
            />
          </Box>
        ))}
      </Box>
    </Box>
  );
}

export default TrafficWebsite;
