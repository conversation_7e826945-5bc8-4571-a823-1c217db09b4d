import { Box, Typography, LinearProgress } from "@mui/material";

function TrafficWebsite() {
  const trafficData = [
    { name: "Google", percentage: 85, color: "#4285f4" },
    { name: "YouTube", percentage: 70, color: "#ff0000" },
    { name: "Instagram", percentage: 60, color: "#e4405f" },
    { name: "Pinterest", percentage: 45, color: "#bd081c" },
    { name: "Facebook", percentage: 40, color: "#1877f2" },
    { name: "Twitter", percentage: 30, color: "#1da1f2" },
  ];

  return (
    <Box
      sx={{
        bgcolor: "#f8f9fa",
        p: 3,
        borderRadius: 2,
        width: 300,
        height: "fit-content",
      }}
    >
      <Typography
        variant="h6"
        sx={{
          fontWeight: "bold",
          mb: 3,
          color: "#333",
        }}
      >
        Traffic by Website
      </Typography>

      <Box sx={{ display: "flex", flexDirection: "column", gap: 2.5 }}>
        {trafficData.map((item, index) => (
          <Box key={index}>
            <Box
              sx={{
                display: "flex",
                justifyContent: "space-between",
                alignItems: "center",
                mb: 1,
              }}
            >
              <Typography
                variant="body2"
                sx={{
                  color: "#666",
                  fontWeight: 500,
                }}
              >
                {item.name}
              </Typography>
              <Typography
                variant="body2"
                sx={{
                  color: "#666",
                  fontSize: "12px",
                }}
              >
                {item.percentage}%
              </Typography>
            </Box>
            <LinearProgress
              variant="determinate"
              value={item.percentage}
              sx={{
                height: 6,
                borderRadius: 3,
                bgcolor: "#e0e0e0",
                "& .MuiLinearProgress-bar": {
                  bgcolor: item.color,
                  borderRadius: 3,
                },
              }}
            />
          </Box>
        ))}
      </Box>
    </Box>
  );
}

export default TrafficWebsite;
